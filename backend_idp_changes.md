# Backend Changes for IDP-Initiated Login Support

## Overview
Add support for Identity Provider (IDP) initiated login while maintaining the existing PKCE authentication flow. This allows users to start the login process from Ok<PERSON>'s dashboard while preserving security.

## Required Changes

### 1. Add IDP-Initiated <PERSON><PERSON> Handler

Create a new handler function in your auth handlers file (likely `handlers/auth.go` or similar):

```go
// HandleIDPInitiatedLogin handles login initiated from Ok<PERSON>
func (h *AuthHandler) HandleIDPInitiatedLogin(c *gin.Context) {
    // Extract any state/context from query params
    returnTo := c.Query("return_to")
    if returnTo == "" {
        returnTo = "/dashboard" // default redirect after login
    }
    
    // Store return URL in session for post-auth redirect
    session := sessions.Default(c)
    session.Set("return_to", returnTo)
    session.Save()
    
    // Redirect to standard login flow
    c.Redirect(http.StatusFound, "/login")
}
```

### 2. Add Route for IDP Login

Add this route to your router setup:

```go
// Add this route alongside your existing auth routes
router.GET("/idp-login", authHandler.HandleIDPInitiatedLogin)
```

### 3. Update Login Callback Handler

Modify your existing login callback handler to support the return_to parameter. Find the section where you redirect back to the frontend after successful authentication and update it:

```go
// In your HandleLoginCallback function, after successful token exchange:
session := sessions.Default(c)
returnTo, exists := session.Get("return_to")
if !exists {
    returnTo = "/dashboard" // default fallback
}
session.Delete("return_to") // clean up session
session.Save()

// Update the redirect URL to include return_to parameter
redirectURL := fmt.Sprintf("%s?session=%s&return_to=%s", 
    h.config.FrontendURL, sessionID, url.QueryEscape(returnTo.(string)))
c.Redirect(http.StatusFound, redirectURL)
```

### 4. Environment Variables

Ensure your backend has the frontend URL configured for redirects. This should already exist but verify:

```env
FRONTEND_URL=http://localhost:3000
```

## Testing

1. Test SP-initiated flow (existing): Navigate to `/login` directly
2. Test IDP-initiated flow (new): Navigate to `/idp-login?return_to=/some-page`
3. Verify both flows end up at the same PKCE authentication process
4. Confirm return_to parameter is preserved through the authentication flow

## Security Notes

- The return_to parameter should be validated to prevent open redirects
- Consider adding URL validation to ensure return_to points to your application domain
- The session-based approach prevents tampering with the return URL during the OAuth flow

## Integration with Okta

After implementing these changes, configure Okta:
- Set "Initiate login URI" to: `http://localhost:3000/idp-login`
- Set "Login initiated by" to: "Either Okta or App"
- Choose "Redirect to app to initiate login (OIDC Compliant)"