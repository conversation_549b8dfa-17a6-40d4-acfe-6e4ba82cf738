# Frontend Changes for IDP-Initiated Login Support

## Overview
Update the Vue3 frontend to handle IDP-initiated login flows while maintaining the existing PKCE authentication. This involves updating the router, auth store, and adding a new route handler.

## Required Changes

### 1. Add IDP Login Route

Add a new route to handle IDP-initiated login in `src/router/index.ts`:

```typescript
const routes: RouteRecordRaw[] = [
  // ... your existing routes
  {
    path: '/idp-login',
    name: 'IDPLogin',
    beforeEnter: (to, from, next) => {
      // Extract return_to parameter and redirect to backend
      const returnTo = to.query.return_to as string
      const loginUrl = new URL('/idp-login', import.meta.env.VITE_API_BASE_URL)
      if (returnTo) {
        loginUrl.searchParams.set('return_to', returnTo)
      }
      // Redirect to backend to start PKCE flow
      window.location.href = loginUrl.href
    }
  }
]
```

### 2. Update Router Navigation Guard

Modify your existing router guard in `src/router/index.ts` to handle the return_to parameter:

```typescript
// Update your existing beforeEach guard
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()

  // Handle Okta callback with PKCE session exchange
  if (to.query.session) {
    const success = await authStore.initializeFromCallback()
    if (success) {
      // Check for return_to parameter from IDP-initiated flow
      const returnTo = to.query.return_to as string
      if (returnTo && returnTo.startsWith('/')) {
        next(returnTo)
      } else {
        next('/dashboard') // default redirect
      }
    } else {
      next('/')
    }
    return
  }

  // ... rest of your existing guard logic
});
```

### 3. Update Auth Store

Modify the `initializeFromCallback` method in `src/store/auth.ts` to preserve the return_to parameter:

```typescript
// Update the existing initializeFromCallback method
async initializeFromCallback() {
  const urlParams = new URLSearchParams(window.location.search)
  const sessionId = urlParams.get('session')

  if (sessionId) {
    try {
      // Exchange session ID for JWT tokens
      const response = await axios.post(`${import.meta.env.VITE_API_BASE_URL}/auth/exchange`, {
        session_id: sessionId
      })

      this.setTokens(response.data.access, response.data.refresh)

      // Clean URL but preserve return_to for router navigation
      const returnTo = urlParams.get('return_to')
      const cleanUrl = window.location.pathname + (returnTo ? `?return_to=${encodeURIComponent(returnTo)}` : '')
      window.history.replaceState({}, document.title, cleanUrl)
      
      return true
    } catch (error) {
      console.error('Failed to exchange session for tokens:', error)
      // Clean URL even on error
      window.history.replaceState({}, document.title, window.location.pathname)
      return false
    }
  }
  return false
},
```

### 4. Update Environment Variables

Ensure your environment files have the correct backend URL. This should already exist:

```env
# .env.development
VITE_API_BASE_URL=http://localhost:8080

# .env.production  
VITE_API_BASE_URL=https://api.stellar.byteorbit.com

# .env.qa
VITE_API_BASE_URL=https://api-qa.stellar.byteorbit.com
```

### 5. Optional: Add Return URL Validation

For security, consider adding validation to ensure return_to URLs are safe:

```typescript
// Helper function to validate return URLs
function isValidReturnUrl(url: string): boolean {
  // Only allow relative URLs starting with /
  return url.startsWith('/') && !url.startsWith('//')
}

// Use in router guard:
const returnTo = to.query.return_to as string
if (returnTo && isValidReturnUrl(returnTo)) {
  next(returnTo)
} else {
  next('/dashboard')
}
```

## Testing

1. **SP-Initiated Flow (existing)**: 
   - Navigate to your app
   - Click login button
   - Should work as before

2. **IDP-Initiated Flow (new)**:
   - Navigate directly to `http://localhost:3000/idp-login`
   - Should redirect to backend and start PKCE flow
   - After auth, should land on dashboard

3. **IDP with Return URL**:
   - Navigate to `http://localhost:3000/idp-login?return_to=/some-page`
   - After auth, should land on `/some-page`

## Integration Notes

- The `/idp-login` route acts as a bridge between Okta and your backend
- All authentication still goes through the same secure PKCE flow
- The return_to parameter is preserved throughout the entire authentication process
- No changes needed to existing components or stores

## Okta Configuration

After implementing these changes, update your Okta application:
- **Initiate login URI**: `http://localhost:3000/idp-login` (adjust for your domain)
- **Login initiated by**: Either Okta or App  
- **Login flow**: Redirect to app to initiate login (OIDC Compliant)